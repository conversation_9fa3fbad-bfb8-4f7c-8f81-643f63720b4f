/**
 * NewsManager - Main container component for news management
 * Orchestrates data fetching, filtering, and state management
 */

"use client";

import { useNewsData } from "@/hooks/useNewsData";
import { useNewsFilters } from "@/hooks/useNewsFilters";
import { useViewPreference } from "@/hooks/useLocalStorage";
import { NewsHeader } from "./NewsHeader";
import { NewsContent } from "./NewsContent";
import { NewsStats } from "./news-stats";
import { SearchFilters } from "./search-filters";
import { ErrorState } from "./error-state";
import { NewsSkeleton } from "./news-skeleton";
import { NewsListSkeleton } from "./news-list-skeleton";
import { Newspaper, Loader2 } from "lucide-react";

/**
 * Props interface for NewsManager component
 */
interface NewsManagerProps {
  className?: string;
}

/**
 * Main news management component
 * Handles all news operations and coordinates child components
 */
export function NewsManager({ className }: NewsManagerProps) {
  // Data management hooks
  const { newsData, loading, error, fetchNews, resetAllNews, deleteNews } =
    useNewsData();

  // Filtering and sorting
  const {
    filters,
    setSearch,
    setAuthor,
    setDate,
    setSort,
    clearFilters,
    hasActiveFilters,
    filteredNews,
    filteredCount,
    uniqueAuthors,
  } = useNewsFilters(newsData);

  // View preferences
  const { value: viewMode, setValue: setViewMode } = useViewPreference();

  // Derived state
  const totalNews = Object.keys(newsData).length;
  const hasNews = totalNews > 0;
  const hasFilteredResults = filteredNews.length > 0;

  /**
   * Handle news deletion with local state update
   */
  const handleDeleteNews = (deletedKey: string) => {
    deleteNews(deletedKey);
  };

  /**
   * Handle view mode change with persistence
   */
  const handleViewChange = (view: "grid" | "list") => {
    setViewMode(view);
  };

  /**
   * Loading state component
   */
  if (loading) {
    return (
      <div
        className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 ${
          className || ""
        }`}
      >
        <div className="container mx-auto px-4 py-8">
          {/* Header with loading */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Newspaper className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  CNEWS | News Admin
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-gray-600">Loading news...</span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="bg-white p-4 rounded-lg border animate-pulse"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg" />
                  <div className="space-y-2 flex-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4" />
                    <div className="h-6 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Content skeleton based on view mode */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <NewsSkeleton key={index} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <NewsListSkeleton key={index} />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  /**
   * Error state component
   */
  if (error) {
    return (
      <div
        className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 ${
          className || ""
        }`}
      >
        <div className="container mx-auto px-4 py-8">
          <NewsHeader
            totalNews={0}
            filteredCount={0}
            viewMode={viewMode}
            onViewChange={handleViewChange}
            onRefresh={fetchNews}
            onReset={resetAllNews}
            isResetting={false}
          />
          <ErrorState message={error} onRetry={fetchNews} />
        </div>
      </div>
    );
  }

  /**
   * Main content render
   */
  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 ${
        className || ""
      }`}
    >
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <NewsHeader
          totalNews={totalNews}
          filteredCount={filteredCount}
          viewMode={viewMode}
          onViewChange={handleViewChange}
          onRefresh={fetchNews}
          onReset={resetAllNews}
          isResetting={loading}
        />

        {/* Stats and Filters - only show if we have news */}
        {hasNews && (
          <>
            <NewsStats newsData={newsData} />

            <SearchFilters
              onSearch={setSearch}
              onFilterByAuthor={setAuthor}
              onFilterByDate={setDate}
              onSortChange={setSort}
              onClearFilters={clearFilters}
              authors={uniqueAuthors}
              activeFilters={filters}
            />
          </>
        )}

        {/* Main Content */}
        <NewsContent
          hasNews={hasNews}
          hasFilteredResults={hasFilteredResults}
          hasActiveFilters={hasActiveFilters}
          filteredNews={filteredNews}
          viewMode={viewMode}
          onDeleteNews={handleDeleteNews}
          onRefresh={fetchNews}
          onClearFilters={clearFilters}
        />
      </div>
    </div>
  );
}

/**
 * Type exports for use in other modules
 */
export type { NewsManagerProps };
