"use client";

import Image from "next/image";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { NewsActions } from "@/components/news-actions";
import { Clock, User, ExternalLink } from "lucide-react";
import { NewsItem } from "@/types/news";
import { useNewsActions } from "@/hooks/useNewsActions";

interface NewsListItemProps {
  newsItem: NewsItem;
  newsKey: string;
  onDelete: (key: string) => void;
}

export function NewsListItem({
  newsItem,
  newsKey,
  onDelete,
}: NewsListItemProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Use the new actions hook
  const { isDeleting, isSending, deleteNews, sendNews } = useNewsActions({
    onDeleteSuccess: (key) => onDelete(key),
    onSendSuccess: () => {
      // Success handling is done by the hook
    },
  });

  const handleDelete = async () => {
    await deleteNews(newsKey);
  };

  const handleSend = async () => {
    if (!newsItem.newsUrl) {
      return;
    }
    await sendNews(newsItem.newsUrl);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return "Hoje";
      if (diffDays === 2) return "Ontem";
      if (diffDays <= 7) return `${diffDays - 1} dias atrás`;

      return date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <div className="group bg-white border border-gray-200 rounded-lg p-3 sm:p-4 hover:shadow-md transition-all duration-200 hover:border-gray-300">
      <div className="flex items-start gap-3 sm:gap-4">
        {/* Imagem em miniatura */}
        {newsItem.newsImg && !imageError && (
          <div className="shrink-0 w-16 h-12 sm:w-20 sm:h-16 lg:w-24 lg:h-20 rounded-lg overflow-hidden bg-gray-100 relative">
            <div
              className={`transition-opacity duration-300 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
            >
              <Image
                src={newsItem.newsImg}
                alt={newsItem.newsTitle || "Notícia"}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
                sizes="(max-width: 640px) 64px, (max-width: 1024px) 80px, 96px"
              />
            </div>
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="w-3 h-3 sm:w-4 sm:h-4 border border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              </div>
            )}
          </div>
        )}

        {/* Conteúdo principal */}
        <div className="flex-1 min-w-0 space-y-2 sm:space-y-3">
          {/* Header com autor e data */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <Avatar className="h-7 w-7 sm:h-8 sm:w-8 ring-2 ring-gray-100 shrink-0">
                <AvatarImage
                  src={newsItem.newsAuthorImg}
                  alt={newsItem.newsAuthor}
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-xs">
                  {newsItem.newsAuthor
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .slice(0, 2)
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>

              <div className="min-w-0 flex-1">
                <div className="flex items-center space-x-2">
                  <User className="h-3 w-3 text-gray-400 shrink-0" />
                  <p className="font-medium text-gray-900 text-sm truncate">
                    {newsItem.newsAuthor}
                  </p>
                </div>
                <div className="flex items-center space-x-2 mt-0.5">
                  <Clock className="h-3 w-3 text-gray-400 shrink-0" />
                  <p className="text-xs text-gray-500 truncate">
                    {formatDate(newsItem.newsDate)}
                  </p>
                </div>
              </div>
            </div>

            {/* Badge e Actions */}
            <div className="flex items-center justify-between sm:justify-end gap-2 sm:gap-3 shrink-0">
              {newsItem.selected && (
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800 hover:bg-green-100 text-xs shrink-0"
                >
                  ✓ Selecionado
                </Badge>
              )}

              <div className="ml-auto sm:ml-0">
                <NewsActions
                  newsKey={newsKey}
                  newsUrl={newsItem.newsUrl}
                  newsAuthor={newsItem.newsAuthor}
                  onDelete={handleDelete}
                  onSend={handleSend}
                  isDeleting={isDeleting}
                  isSending={isSending}
                />
              </div>
            </div>
          </div>

          {/* Título */}
          {newsItem.newsTitle && (
            <h3 className="font-semibold text-sm sm:text-base leading-tight text-gray-900 line-clamp-2 group-hover:text-blue-700 transition-colors">
              {newsItem.newsTitle}
            </h3>
          )}

          {/* Conteúdo */}
          <div className="space-y-2">
            <p className="text-gray-700 text-xs sm:text-sm leading-relaxed line-clamp-2">
              {newsItem.newsContent}
            </p>

            {newsItem.newsUrl && (
              <a
                href={newsItem.newsUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
              >
                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                <span>Ler artigo completo</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
